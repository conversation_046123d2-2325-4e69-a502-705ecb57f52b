# Local LLM Studio

A powerful, self-hosted environment that maximizes the effectiveness of local LLMs through intelligent prompt enhancement and systematic conversation archiving.

## 🚀 Features

- **Super Saiyan Multi-Agent Prompt Enhancement**: Transforms simple user queries into expert-level prompts using a simulated team of AI agents
- **Dual-Pane Interface**: Clean separation between input controls and output display
- **Real-time Streaming**: Token-by-token response generation with visual feedback
- **Thinking Process Visualization**: See how the model arrives at its conclusions
- **Automatic Conversation Archiving**: All interactions saved as structured Markdown files
- **Model Selection**: Easy switching between available Ollama models

## 🏗️ Architecture

### Backend (Python + FastAPI)
- **FastAPI**: High-performance async API server
- **Ollama Integration**: Seamless communication with local LLM instances
- **Streaming Support**: Real-time response generation via Server-Sent Events
- **Automatic Archiving**: Background conversation logging

### Frontend (Next.js + React)
- **Next.js**: Modern React framework with TypeScript
- **Tailwind CSS**: Utility-first styling with responsive design
- **Real-time Updates**: Live streaming response display
- **Tabbed Interface**: Separate views for final answers and thinking processes

## 📋 Prerequisites

1. **Python 3.8+** with pip
2. **Node.js 18+** with npm
3. **Ollama** installed and running locally
4. At least one LLM model downloaded in Ollama

## 🛠️ Installation & Setup

### 1. Clone and Setup Backend

```bash
cd backend
pip install -r requirements.txt
```

### 2. Configure Environment

Edit `backend/.env` to match your setup:
```env
OLLAMA_BASE_URL=http://localhost:11434
ENHANCER_MODEL=qwen3:30b-a3b-thinking-2507-q4_K_M
```

### 3. Setup Frontend

```bash
cd frontend
npm install
```

### 4. Start the Services

**Terminal 1 - Backend:**
```bash
cd backend
python main.py
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```

## 🎯 Usage

1. Open http://localhost:3000 in your browser
2. Select your preferred model from the dropdown
3. Enter your question or prompt
4. Click "Go Super Saiyan & Generate"
5. Watch the real-time response generation
6. Switch between "Chat Response" and "Thinking Process" tabs

## 📁 Project Structure

```
local-llm-studio/
├── backend/
│   ├── main.py              # FastAPI application
│   ├── requirements.txt     # Python dependencies
│   ├── .env                 # Environment configuration
│   └── templates/
│       └── archive_template.md  # Markdown template for archiving
├── frontend/
│   ├── src/app/
│   │   ├── page.tsx         # Main UI component
│   │   ├── layout.tsx       # App layout
│   │   └── globals.css      # Global styles
│   ├── package.json         # Node.js dependencies
│   └── tailwind.config.js   # Tailwind configuration
└── conversations/           # Auto-generated conversation archives
    └── YYYY-MM/DD/         # Organized by date
```

## 🔧 Configuration

### Backend Configuration (.env)
- `OLLAMA_BASE_URL`: URL of your Ollama instance (default: http://localhost:11434)
- `ENHANCER_MODEL`: Model used for prompt enhancement (should be a powerful model)

### Frontend Configuration
- The frontend automatically connects to the backend at http://localhost:8000
- CORS is configured to allow requests from http://localhost:3000

## 📝 Conversation Archiving

All conversations are automatically saved as Markdown files in the `conversations/` directory:

- **Structure**: `conversations/YYYY-MM/DD/YYYYMMDD_HHMMSS_prompt-slug.md`
- **Content**: Original prompt, enhanced prompt, thinking process, and final answer
- **Format**: Clean, readable Markdown suitable for review and analysis

## 🤖 The Super Saiyan Enhancement System

The core innovation of this project is the multi-agent prompt enhancement system:

1. **Query Analyst**: Deconstructs the user's query and identifies core intent
2. **Domain Expert**: Provides relevant context and expands technical terms
3. **Planning Agent**: Structures the ideal response format
4. **Persona Agent**: Assigns an expert persona to the responding model

This collaborative approach transforms simple queries into comprehensive, expert-level prompts that dramatically improve response quality.

## 🚨 Troubleshooting

### Backend Issues
- Ensure Ollama is running: `ollama serve`
- Check if your enhancer model is available: `ollama list`
- Verify Python dependencies are installed

### Frontend Issues
- Clear npm cache: `npm cache clean --force`
- Reinstall dependencies: `rm -rf node_modules && npm install`
- Check browser console for JavaScript errors

### Connection Issues
- Verify backend is running on port 8000
- Check CORS settings if accessing from different origins
- Ensure firewall isn't blocking local connections

## 🔮 Future Enhancements

- **Session Management**: Multi-turn conversations with context
- **Model Comparison**: Side-by-side responses from different models
- **Search & Analytics**: Full-text search across conversation history
- **Custom Enhancement Templates**: User-defined prompt enhancement strategies
- **Export Options**: PDF, JSON, and other export formats

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
